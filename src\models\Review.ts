import mongoose, { Schema } from 'mongoose';
import { IReview } from '../types';

const reviewSchema = new Schema<IReview>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false // Allow anonymous reviews for appointments
  },
  appointment: {
    type: Schema.Types.ObjectId,
    ref: 'Appointment',
    required: false // For appointment-based reviews
  },
  product: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: false
  },
  service: {
    type: Schema.Types.ObjectId,
    ref: 'Service',
    required: false
  },
  customerName: {
    type: String,
    trim: true,
    maxlength: [100, 'Customer name cannot be more than 100 characters']
  },
  customerEmail: {
    type: String,
    trim: true,
    lowercase: true,
    maxlength: [255, 'Customer email cannot be more than 255 characters']
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot be more than 5']
  },
  title: {
    type: String,
    required: false, // Make title optional for appointment reviews
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  comment: {
    type: String,
    required: false, // Make comment optional
    trim: true,
    maxlength: [1000, 'Comment cannot be more than 1000 characters']
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  isVerifiedPurchase: {
    type: Boolean,
    default: false
  },
  helpfulVotes: {
    type: Number,
    default: 0
  },
  adminResponse: {
    type: String,
    trim: true,
    maxlength: [500, 'Admin response cannot be more than 500 characters']
  },
  adminResponseDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes for better query performance
reviewSchema.index({ product: 1, status: 1 });
reviewSchema.index({ service: 1, status: 1 });
reviewSchema.index({ appointment: 1 });
reviewSchema.index({ user: 1 });
reviewSchema.index({ status: 1 });
reviewSchema.index({ createdAt: -1 });

// Ensure a user can only review a product once (for product reviews)
reviewSchema.index({ user: 1, product: 1 }, { unique: true, sparse: true });

// For service-only reviews (not appointment-based), ensure one review per user per service
// Note: This only applies when both user and service are present and appointment is null
reviewSchema.index(
  { user: 1, service: 1 },
  {
    unique: true,
    sparse: true,
    partialFilterExpression: {
      appointment: { $exists: false },
      service: { $exists: true, $ne: null }
    }
  }
);

// One review per appointment (for appointment-based reviews)
reviewSchema.index({ appointment: 1 }, { unique: true, sparse: true });

// Virtual for populated user data
reviewSchema.virtual('userData', {
  ref: 'User',
  localField: 'user',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated product data
reviewSchema.virtual('productData', {
  ref: 'Product',
  localField: 'product',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated service data
reviewSchema.virtual('serviceData', {
  ref: 'Service',
  localField: 'service',
  foreignField: '_id',
  justOne: true
});

// Ensure virtuals are included in JSON output
reviewSchema.set('toJSON', { virtuals: true });
reviewSchema.set('toObject', { virtuals: true });

// Custom validation to ensure either product, service, or appointment is provided
reviewSchema.pre('validate', function(next) {
  const hasProduct = !!this.product;
  const hasService = !!this.service;
  const hasAppointment = !!this.appointment;

  const count = [hasProduct, hasService, hasAppointment].filter(Boolean).length;

  if (count === 0) {
    next(new Error('Review must be associated with either a product, service, or appointment'));
  } else if (count > 1) {
    next(new Error('Review can only be associated with one of: product, service, or appointment'));
  } else {
    next();
  }
});

export const Review = mongoose.model<IReview>('Review', reviewSchema);
